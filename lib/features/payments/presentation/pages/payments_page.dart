import 'package:flutter/material.dart';
import 'package:yaplin_app/features/payments/presentation/widgets/last_payments_view/last_payments_view.dart';
import 'package:yaplin_app/features/payments/presentation/widgets/payments_menu/payments_menu.dart';

class PaymentsPage extends StatelessWidget {
  const PaymentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
      appBar: AppBar(backgroundColor: Colors.transparent, ),
      drawer: Drawer(),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 48.0),
            child: Column(
              children: [
                Text(
                  'Tus Ventas',
                  style: Theme.of(context).textTheme.labelMedium,
                ),
                Text(
                  'S/ 250.00',
                  style: Theme.of(context).textTheme.displayMedium,
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.0),
                  topRight: Radius.circular(16.0),
                ),
                color: Theme.of(context).colorScheme.primaryContainer,
              ),
              child: <PERSON>um<PERSON>(
                children: [
                  PaymentsMenu(),
                  SizedBox(height: 32),
                  Expanded(child: LastPaymentsView()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
