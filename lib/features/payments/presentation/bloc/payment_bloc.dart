import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:yaplin_app/features/payments/usecases/scan_and_save_payment/scan_and_save_payment.dart';

part 'payment_event.dart';

part 'payment_state.dart';

class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final ScanAndSavePayment scanAndSavePayment;

  PaymentBloc(this.scanAndSavePayment) : super(PaymentInitial()) {
    on<ScanAndSavePaymentRequested>((event, emit) async {
      emit(PaymentLoading());

      final result = await scanAndSavePayment();
      result.fold(
        (failure) => emit(PaymentFailure(failure.message)),
        (_) => emit(PaymentSavedSuccessfully()),
      );
    });
  }
}
