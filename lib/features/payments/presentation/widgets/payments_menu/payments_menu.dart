import 'package:flutter/material.dart';

class PaymentsMenu extends StatelessWidget {
  const PaymentsMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _PaymentsMenuOption(
          name: 'Escanear',
          icon: Icons.document_scanner_outlined,
          onPressed: () {},
        ),
        _PaymentsMenuOption(
          name: '<PERSON>ñadir Cobro',
          icon: Icons.add,
          onPressed: () {},
        ),
        _PaymentsMenuOption(
          name: 'Reporte',
          icon: Icons.description_outlined,
          onPressed: () {},
        ),
        _PaymentsMenuOption(
          name: 'Más',
          icon: Icons.more_horiz,
          onPressed: () {},
        ),
      ],
    );
  }
}

class _PaymentsMenuOption extends StatelessWidget {
  final String name;
  final VoidCallback onPressed;
  final IconData icon;

  const _PaymentsMenuOption({
    required this.name,
    required this.onPressed,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 8.0, top: 32.0, right: 8.0),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16.0),
              color: Theme.of(context).colorScheme.secondaryContainer,
            ),
            child: IconButton(onPressed: onPressed, icon: Icon(icon)),
          ),
          SizedBox(height: 8.0),
          Text(name, style: Theme.of(context).textTheme.labelSmall),
        ],
      ),
    );
  }
}
