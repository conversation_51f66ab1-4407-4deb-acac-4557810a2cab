import 'package:flutter/material.dart';
import 'package:yaplin_app/features/payments/domain/entities/payment.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/amount.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/operation_number.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/payment_source.dart';
import 'package:yaplin_app/features/payments/presentation/widgets/payment_item/payment_item.dart';

class LastPaymentsView extends StatelessWidget {
  const LastPaymentsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text('Cobros de hoy',style: Theme.of(context).textTheme.titleLarge,),
        ),
        SizedBox(height: 16.0),
        Expanded(
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) {
              final payment = Payment(
                operationNumber: OperationNumber('123456789'),
                timeStamp: DateTime.now(),
                amount: Amount(100.0),
                source: PaymentSource(PaymentPlatform.yape),
              );
              return PaymentItem(payment: payment);
            },
          ),
        ),
      ],
    );
  }
}
