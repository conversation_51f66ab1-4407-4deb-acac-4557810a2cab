import 'package:flutter/material.dart';
import 'package:yaplin_app/core/date_time/date_time_extensions.dart';
import 'package:yaplin_app/features/payments/domain/entities/payment.dart';

class PaymentItem extends StatelessWidget {
  final Payment payment;

  const PaymentItem({super.key, required this.payment});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
        child: payment.source!.isYape ? Text('y') : Text('p'),
      ),
      title: Text(
        payment.amount!.displayValue,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      subtitle: Text(
        payment.operationNumber!.displayValue,
        style: Theme.of(context).textTheme.labelSmall,
      ),
      trailing: Text(payment.timeStamp!.displayValue),
    );
  }
}
