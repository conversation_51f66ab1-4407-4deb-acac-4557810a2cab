import 'package:dartz/dartz.dart';
import 'package:yaplin_app/core/errors/failure.dart';
import 'package:yaplin_app/features/payments/data/models/payment_model.dart';
import 'package:yaplin_app/features/payments/domain/errors/errors.dart';
import 'package:yaplin_app/features/payments/domain/repositories/payment_repository.dart';
import 'package:yaplin_app/features/payments/services/camera_service.dart';
import 'package:yaplin_app/features/payments/services/gemini_service.dart';

class ScanAndSavePayment {
  final CameraService cameraService;
  final GeminiService geminiService;
  final PaymentRepository repository;

  ScanAndSavePayment({
    required this.cameraService,
    required this.geminiService,
    required this.repository,
  });

  Future<Either<Failure, Unit>> call() async {
    try {
      final imageFile = await cameraService.capturePhoto();
      final result = await geminiService.extractPaymentData(imageFile!);
      final payment = PaymentModel.fromJson(result).toEntity();
      await repository.save(payment);
      return right(unit);
    } catch (e) {
      return left(CameraFailure(e.toString()));
    }
  }
}
