import 'package:dartz/dartz.dart';
import 'package:yaplin_app/core/errors/failure.dart';
import 'package:yaplin_app/features/payments/domain/entities/payment.dart';
import 'package:yaplin_app/features/payments/domain/errors/errors.dart';
import 'package:yaplin_app/features/payments/domain/repositories/payment_repository.dart';

class GetTodayPayments {
  final PaymentRepository repository;

  GetTodayPayments({required this.repository});

  Future<Either<Failure, List<Payment>>> call() async {
    final now = DateTime.now();
    try {
      final payments = await repository.getForDate(now);
      return right(payments);
    } catch (e) {
      return left(ServerFailure(e.toString()));
    }
  }
}
