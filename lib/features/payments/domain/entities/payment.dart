import 'package:yaplin_app/core/entity/entity.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/amount.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/operation_number.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/payment_source.dart';

class Payment extends Entity {
  final Amount? amount;
  final OperationNumber? operationNumber;
  final PaymentSource? source;
  final DateTime? timeStamp;

  const Payment({
    super.uuid,
    this.amount,
    this.operationNumber,
    this.source,
    this.timeStamp,
  });

  @override
  List<Object?> get props => [
    uuid,
    amount,
    operationNumber,
    source,
    timeStamp,
  ];
}
