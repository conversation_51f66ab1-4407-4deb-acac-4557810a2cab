import 'package:yaplin_app/core/value_objects/value_object.dart';

enum PaymentPlatform {
  yape(1),
  plin(1);

  final int apiValue;
  const PaymentPlatform(this.apiValue);
}

class PaymentSource extends ValueObject<PaymentPlatform> {
  const PaymentSource(super.value);

  factory PaymentSource.fromApiValue(int input) {
    switch (input) {
      case 0:
        return const PaymentSource(PaymentPlatform.yape);
      case 1:
        return const PaymentSource(PaymentPlatform.plin);
      default:
        throw ArgumentError('Unknown payment platform: $input');
    }
  }

  @override
  String get displayValue {
    switch (value) {
      case PaymentPlatform.yape:
        return 'Yape';
      case PaymentPlatform.plin:
        return 'Plin';
    }
  }

  @override
  PaymentPlatform get apiValue => value;

  bool get isYape => value == PaymentPlatform.yape;

  bool get isPlin => value == PaymentPlatform.plin;
}
