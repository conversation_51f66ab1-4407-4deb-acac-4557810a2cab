import 'dart:convert';
import 'dart:io';

import 'package:firebase_ai/firebase_ai.dart';

class GeminiService {
  final GenerativeModel _model;

  GeminiService()
    : _model = FirebaseAI.googleAI().generativeModel(model: 'gemini-2.5-flash');

  Future<Map<String, dynamic>> extractPaymentData(File imageFile) async {
    final prompt = '''
Extrae la siguiente información del comprobante de pago:
- Fuente del pago: puede ser Yape o Plin
- Monto pagado (en soles)
- <PERSON><PERSON> del pago (en formato ISO 8601)

Devuelve un JSON con la siguiente forma:
{
  "source": "yape" | "plin",
  "amount": 00.00,
  "date": "YYYY-MM-DDTHH:MM:SS"
}
''';

    final content = Content.multi([
      TextPart(prompt),
      InlineDataPart('image/jpeg', await imageFile.readAsBytes()),
    ]);

    final response = await _model.generateContent([content]);
    final text = response.text;

    try {
      final extracted = _parseJson(text ?? '');
      return extracted;
    } catch (_) {
      throw Exception('No se pudo extraer datos del comprobante');
    }
  }

  Map<String, dynamic> _parseJson(String rawJson) {
    final cleaned = rawJson.trim();

    return Map<String, dynamic>.from(jsonDecode(cleaned));
  }
}
