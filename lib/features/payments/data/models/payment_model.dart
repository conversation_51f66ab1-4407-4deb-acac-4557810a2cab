import 'package:yaplin_app/core/data_model/data_model.dart';
import 'package:yaplin_app/core/date_time/date_time_extensions.dart';
import 'package:yaplin_app/core/value_objects/uuid.dart';
import 'package:yaplin_app/features/payments/domain/entities/payment.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/amount.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/operation_number.dart';
import 'package:yaplin_app/features/payments/domain/value_objects/payment_source.dart';

class PaymentModel extends DataModel<Payment> {
  final double amount;
  final int source;
  final String operationNumber;
  final String timeStamp;

  PaymentModel({
    super.uuid,
    required this.source,
    required this.amount,
    required this.operationNumber,
    required this.timeStamp,
  });

  factory PaymentModel.fromEntity(Payment entry) {
    return PaymentModel(
      uuid: entry.uuid!.apiValue,
      amount: entry.amount!.apiValue,
      source: entry.source!.apiValue.apiValue,
      operationNumber: entry.operationNumber!.apiValue,
      timeStamp: entry.timeStamp!.apiValue,
    );
  }

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      uuid: json['uuid'],
      amount: json['amount'],
      source: json['source'],
      operationNumber: json['operation_number'],
      timeStamp: json['timestamp'],
    );
  }

  @override
  Payment toEntity() {
    return Payment(
      uuid: Uuid(uuid!),
      amount: Amount(amount),
      source: PaymentSource.fromApiValue(source),
      operationNumber: OperationNumber(operationNumber),
      timeStamp: DateTime.parse(timeStamp),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
    'uuid': uuid,
    'amount': amount,
    'source': source,
    'operation_number': operationNumber,
    'timestamp': timeStamp,
  };
}
