import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:yaplin_app/features/payments/data/models/payment_model.dart';

class SupabasePaymentDatasource {
  final SupabaseClient client;

  SupabasePaymentDatasource(this.client);

  Future<void> save(PaymentModel model) async {
    await client.from('payments').insert(model.toJson());
  }

  Future<List<PaymentModel>> getAll() async {
    final response = await client.from('payments').select();
    return (response as List)
        .map((json) => PaymentModel.fromJson(json))
        .toList();
  }

  Future<List<PaymentModel>> getForDate(DateTime date) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final response = await client
        .from('payments')
        .select()
        .gte('created_at', startOfDay.toIso8601String())
        .lt('created_at', endOfDay.toIso8601String())
        .order('created_at', ascending: false);

    return (response as List)
        .map((json) => PaymentModel.fromJson(json))
        .toList();
  }

  Future<void> delete(String id) async {
    await client.from('payments').delete().eq('id', id);
  }
}
