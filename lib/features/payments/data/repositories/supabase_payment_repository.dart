import 'package:yaplin_app/features/payments/data/datasources/supabase_payment_datasource.dart';
import 'package:yaplin_app/features/payments/data/models/payment_model.dart';
import 'package:yaplin_app/features/payments/domain/entities/payment.dart';
import 'package:yaplin_app/features/payments/domain/repositories/payment_repository.dart';

class SupabasePaymentRepository implements PaymentRepository {
  final SupabasePaymentDatasource datasource;

  SupabasePaymentRepository(this.datasource);

  @override
  Future<void> save(Payment entry) {
    final model = PaymentModel.fromEntity(entry);
    return datasource.save(model);
  }

  @override
  Future<List<Payment>> getForDate(DateTime date) async {
    final models = await datasource.getForDate(date);
    return models.map((m) => m.toEntity()).toList();
  }

  @override
  Future<List<Payment>> getAll() async {
    final models = await datasource.getAll();
    return models.map((m) => m.toEntity()).toList();
  }

  @override
  Future<void> delete(String id) {
    return datasource.delete(id);
  }
}
