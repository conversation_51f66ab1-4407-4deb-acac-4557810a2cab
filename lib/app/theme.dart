import 'package:flutter/material.dart';

ThemeData buildAppTheme() {
  return ThemeData.from(
    colorScheme: ColorScheme(
      brightness: Brightness.light,
      primary: Colors.white,
      onPrimary: Colors.black,
      secondary: Color(0xFFF3F3F7),
      onSecondary: Colors.black,
      error: Color(0xFF9C1F00),
      onError: Colors.black,
      surface: Colors.white,
      onSurface: Colors.black,
    ),
    textTheme: TextTheme(
      labelSmall: TextStyle(color: Color(0xFF979797)),
      titleMedium: TextStyle(fontWeight: FontWeight.bold),
    ),
  );
}
