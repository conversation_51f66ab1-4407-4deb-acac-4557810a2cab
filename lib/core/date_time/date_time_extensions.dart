extension DateTimeApiExtensions on DateTime {
  String get apiValue => toIso8601String();

  String get displayValue {
    final now = DateTime.now();
    final isSameDay = now.year == year && now.month == month && now.day == day;

    if (isSameDay) {
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    } else {
      return '${day.toString().padLeft(2, '0')}/${month.toString().padLeft(2, '0')}/$year ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
    }
  }
}
